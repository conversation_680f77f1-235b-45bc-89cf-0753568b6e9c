/**
 * Chat Widget - Floating chatbot widget for all pages
 */

class ChatWidget {
    constructor() {
        this.isOpen = false;
        this.currentStoreId = null;
        this.conversationId = null;
        this.stores = [];
        this.isAuthenticated = false;

        this.init();
    }

    init() {
        this.checkAuthentication();
        this.createWidget();
        this.setupEventListeners();
        this.loadStores();
    }

    checkAuthentication() {
        const token = localStorage.getItem('access_token');
        this.isAuthenticated = !!token;
    }

    createWidget() {
        // Create FAB button
        const fab = document.createElement('button');
        fab.id = 'chat-fab';
        fab.className = 'chat-fab';
        fab.innerHTML = '💬';
        fab.title = 'Open Chat Assistant';

        // Create chat widget
        const widget = document.createElement('div');
        widget.id = 'chat-widget';
        widget.className = 'chat-widget';
        widget.innerHTML = `
            <div class="chat-widget-header">
                <h3>AI Shopping Assistant</h3>
                <button class="chat-close-btn" id="chat-close-btn">×</button>
            </div>
            <div class="chat-widget-body">
                <div class="chat-store-selector" id="chat-store-selector" style="display: none;">
                    <select id="widget-store-select">
                        <option value="">Select a store...</option>
                    </select>
                </div>
                <div class="chat-messages" id="widget-chat-messages">
                    <div class="chat-welcome-message" id="widget-welcome-message">
                        <h4>Welcome! 👋</h4>
                        <p id="widget-welcome-text">I'm your AI shopping assistant. ${this.isAuthenticated ? 'Loading your stores...' : 'Please log in to start chatting with your store assistants.'}</p>
                    </div>
                </div>
                <div class="chat-input-container">
                    <div class="chat-input-form" style="display: flex; align-items: center;">
                        <textarea
                            id="widget-chat-input"
                            class="chat-input"
                            placeholder="Type your message..."
                            rows="1"
                            ${!this.isAuthenticated ? 'disabled' : ''}
                        ></textarea>
                        <button
                            id="widget-send-button"
                            class="chat-send-button"
                            ${!this.isAuthenticated ? 'disabled' : ''}
                        >
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add to page
        document.body.appendChild(fab);
        document.body.appendChild(widget);

        // Store references
        this.fab = fab;
        this.widget = widget;
        this.messagesContainer = document.getElementById('widget-chat-messages');
        this.input = document.getElementById('widget-chat-input');
        this.sendButton = document.getElementById('widget-send-button');
        this.storeSelector = document.getElementById('widget-store-select');
        this.storeSelectorContainer = document.getElementById('chat-store-selector');
    }

    setupEventListeners() {
        // FAB click
        this.fab.addEventListener('click', () => this.toggleWidget());

        // Close button click
        document.getElementById('chat-close-btn').addEventListener('click', () => this.toggleWidget());

        // Send button click
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // Enter key in input
        this.input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Auto-resize textarea
        this.input.addEventListener('input', () => this.autoResizeTextarea());

        // Store selection
        this.storeSelector.addEventListener('change', (e) => this.selectStore(e.target.value));

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.widget.contains(e.target) && !this.fab.contains(e.target)) {
                this.toggleWidget();
            }
        });
    }

    toggleWidget() {
        this.isOpen = !this.isOpen;

        if (this.isOpen) {
            this.widget.classList.add('active');
            this.fab.classList.add('hidden');

            // Focus input after animation
            setTimeout(() => {
                if (this.isAuthenticated && this.currentStoreId) {
                    this.input.focus();
                }
            }, 300);
        } else {
            this.widget.classList.remove('active');
            this.fab.classList.remove('hidden');
        }
    }

    async loadStores() {
        if (!this.isAuthenticated) return;

        try {
            const response = await fetch('/assistants/stores/selection', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                this.stores = await response.json();
                this.populateStoreSelector();

                // Show store selector if user has stores with assistants
                const storesWithAssistants = this.stores.filter(store => store.has_assistant);
                if (storesWithAssistants.length > 0) {
                    this.storeSelectorContainer.style.display = 'block';
                    this.updateWelcomeMessage('Select a store above to start chatting!');
                } else if (this.stores.length > 0) {
                    this.updateWelcomeMessage('You have stores connected, but no assistants are ready yet. Please create assistants for your stores first.');
                } else {
                    this.updateWelcomeMessage('No stores connected yet. Please connect your Shopify stores first to start chatting!');
                }
            } else {
                console.error('Failed to load stores for widget');
                this.updateWelcomeMessage('Failed to load your stores. Please try refreshing the page.');
            }
        } catch (error) {
            console.error('Error loading stores:', error);
            this.updateWelcomeMessage('Network error while loading stores. Please check your connection.');
        }
    }

    populateStoreSelector() {
        this.storeSelector.innerHTML = '<option value="">Select a store...</option>';

        this.stores.forEach(store => {
            const option = document.createElement('option');
            option.value = store.store_id;
            option.textContent = `${store.store_name} (${store.has_assistant ? 'Assistant Ready' : 'No Assistant'})`;
            option.disabled = !store.has_assistant;
            this.storeSelector.appendChild(option);
        });
    }

    selectStore(storeId) {
        if (!storeId) {
            this.currentStoreId = null;
            this.conversationId = null;
            this.input.disabled = true;
            this.sendButton.disabled = true;
            this.showWelcomeMessage();
            return;
        }

        this.currentStoreId = parseInt(storeId);
        this.conversationId = null; // Reset conversation

        const store = this.stores.find(s => s.store_id === this.currentStoreId);
        if (store && store.has_assistant) {
            this.input.disabled = false;
            this.sendButton.disabled = false;
            this.clearMessages();
            this.addMessage(`Hello! I'm the AI assistant for ${store.store_name}. How can I help you today?`, false);
            this.input.focus();
        } else {
            this.input.disabled = true;
            this.sendButton.disabled = true;
            this.clearMessages();
            this.addMessage('This store does not have an assistant yet. Please create an assistant for this store first.', false);
        }
    }

    async sendMessage() {
        const message = this.input.value.trim();
        if (!message || !this.currentStoreId || !this.isAuthenticated) return;

        // Add user message
        this.addMessage(message, true);
        this.input.value = '';
        this.autoResizeTextarea();

        // Show typing indicator
        this.showTypingIndicator();

        try {
            const response = await fetch('/assistants/chat', {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({
                    message: message,
                    store_id: this.currentStoreId,
                    conversation_id: this.conversationId
                })
            });

            this.hideTypingIndicator();

            if (response.ok) {
                const data = await response.json();
                this.conversationId = data.conversation_id;
                this.addMessage(data.response, false);
            } else {
                const error = await response.json();
                this.addMessage('Sorry, there was an error processing your request. Please try again.', false);
            }
        } catch (error) {
            this.hideTypingIndicator();
            this.addMessage('Network error. Please check your connection and try again.', false);
        }
    }

    addMessage(message, isUser) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        // Process message formatting
        let formattedMessage = this.formatMessage(message);
        messageContent.innerHTML = `<p>${formattedMessage}</p>`;

        messageDiv.appendChild(messageContent);
        this.messagesContainer.appendChild(messageDiv);

        // Scroll to bottom
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    formatMessage(message) {
        // Convert bullet points
        let formatted = message.replace(/^[*-] (.+)$/gm, '<li>$1</li>');
        if (formatted.includes('<li>')) {
            formatted = '<ul>' + formatted + '</ul>';
        }

        // Convert headers
        formatted = formatted.replace(/^# (.+)$/gm, '<h3>$1</h3>');
        formatted = formatted.replace(/^## (.+)$/gm, '<h4>$1</h4>');

        // Convert bold and italic
        formatted = formatted.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
        formatted = formatted.replace(/\*(.+?)\*/g, '<em>$1</em>');

        // Convert line breaks
        formatted = formatted.replace(/\n/g, '<br>');

        return formatted;
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant';
        typingDiv.id = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="typing-indicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        this.messagesContainer.appendChild(typingDiv);
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    clearMessages() {
        this.messagesContainer.innerHTML = '';
    }

    updateWelcomeMessage(message) {
        const welcomeText = document.getElementById('widget-welcome-text');
        if (welcomeText) {
            welcomeText.textContent = message;
        }
    }

    showWelcomeMessage() {
        this.clearMessages();
        const welcomeDiv = document.createElement('div');
        welcomeDiv.className = 'chat-welcome-message';
        welcomeDiv.id = 'widget-welcome-message';
        welcomeDiv.innerHTML = `
            <h4>Welcome! 👋</h4>
            <p id="widget-welcome-text">I'm your AI shopping assistant. Select a store above to start chatting!</p>
        `;
        this.messagesContainer.appendChild(welcomeDiv);
    }

    autoResizeTextarea() {
        this.input.style.height = 'auto';
        this.input.style.height = Math.min(this.input.scrollHeight, 120) + 'px';
    }

    getAuthHeaders() {
        const token = localStorage.getItem('access_token');
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        };
    }
}

// Initialize widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChatWidget();
});
