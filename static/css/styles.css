:root {
    --primary-color: #4a90e2;
    --secondary-color: #f5f5f5;
    --accent-color: #ff6b6b;
    --text-color: #333;
    --light-text: #666;
    --border-color: #ddd;
    --assistant-bg: #f1f1f1;
    --user-bg: #4a90e2;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f9f9f9;
    padding: 20px;
    position: relative;
    min-height: 100vh;
}

.site-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Chat Toggle Button */
.chat-toggle-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 999;
    transition: all 0.3s ease;
}

.chat-toggle-button:hover {
    background-color: #3a7bc8;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.chat-toggle-button i {
    font-size: 24px;
}

.chat-toggle-button span {
    display: none;
}

/* Floating Chat Widget */
.chat-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(74, 144, 226, 0.4);
    z-index: 1001;
    transition: all 0.3s ease;
    border: none;
    color: white;
    font-size: 24px;
}

.chat-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(74, 144, 226, 0.6);
}

.chat-fab.hidden {
    transform: scale(0);
    opacity: 0;
}

.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 400px;
    height: 600px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1000;
    transform: translateX(420px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-widget.active {
    transform: translateX(0);
    opacity: 1;
}

.chat-widget-header {
    background: linear-gradient(135deg, var(--primary-color), #357abd);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.chat-widget-header h3 {
    font-size: 18px;
    margin: 0;
    font-weight: 600;
}

.chat-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.chat-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.close-button {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: opacity 0.3s;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    opacity: 0.8;
}

.chat-container {
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 15px;
    scrollbar-width: thin;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background-color: #d1d1d1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}

.message {
    display: flex;
    flex-direction: column;
}

.message.user {
    align-items: flex-end;
}

.message.assistant {
    align-items: flex-start;
}

.message-content {
    max-width: 85%;
    padding: 12px 15px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.user .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: 5px;
}

.assistant .message-content {
    background-color: #f1f1f1;
    color: var(--text-color);
    border-bottom-left-radius: 5px;
}

.message-content p {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.chat-input-container {
    display: flex;
    border-top: 1px solid #eaeaea;
    overflow: hidden;
    background-color: white;
    padding: 10px 15px;
    align-items: center;
}

#user-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    outline: none;
    font-size: 14px;
    margin-right: 10px;
    color: #333;
}

#send-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

#send-button i {
    font-size: 14px;
}

#send-button:hover {
    background-color: #3a7bc8;
}

/* Chat Widget Specific Styles */
.chat-widget-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-store-selector {
    padding: 12px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.chat-store-selector select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
}

.chat-widget .chat-messages {
    background-color: #f8f9fa;
    padding: 20px;
}

.chat-widget .chat-input-container {
    padding: 16px 20px;
}

.chat-widget .chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 24px;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    max-height: 120px;
    min-height: 44px;
    transition: border-color 0.2s;
    margin-right: 12px;
}

.chat-widget .chat-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.chat-send-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    flex-shrink: 0;
}

.chat-send-button:hover:not(:disabled) {
    background-color: #357abd;
    transform: scale(1.05);
}

.chat-send-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
}

.chat-welcome-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.chat-welcome-message h4 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

.chat-error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 8px;
    margin: 10px 20px;
    border: 1px solid #f5c6cb;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background-color: #f1f1f1;
    border-radius: 18px;
    max-width: 80px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background-color: #999;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .site-content {
        padding: 10px;
    }

    .chat-fab {
        bottom: 20px;
        right: 20px;
        width: 56px;
        height: 56px;
        font-size: 22px;
    }

    .chat-widget {
        width: 100vw;
        height: 100vh;
        bottom: 0;
        right: 0;
        border-radius: 0;
        transform: translateY(100vh);
    }

    .chat-widget.active {
        transform: translateY(0);
    }

    .chat-widget-header {
        border-radius: 0;
        padding: 16px 20px;
    }

    .chat-widget .chat-messages {
        padding: 16px;
    }

    .chat-widget .chat-input-container {
        padding: 16px;
    }

    .message-content {
        max-width: 90%;
    }

    .chat-store-selector {
        padding: 12px 16px;
    }
}
