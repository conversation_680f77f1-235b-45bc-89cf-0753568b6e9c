<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Pinecone E-commerce Assistant</title>
    <link href="/static/css/styles.css" rel="stylesheet">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .dashboard-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-info h1 {
            margin: 0;
            color: #333;
        }
        .user-info p {
            margin: 5px 0 0 0;
            color: #666;
        }
        .header-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .dashboard-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .dashboard-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ccc;
        }
        .quick-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            .dashboard-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            .quick-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div class="user-info">
                <h1>Welcome, <span id="user-name">Loading...</span>!</h1>
                <p>Email: <span id="user-email">Loading...</span></p>
            </div>
            <div class="header-actions">
                <a href="/profile" class="btn btn-secondary">Profile Settings</a>
                <button id="logout-btn" class="btn btn-danger">Logout</button>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Stores Section -->
            <div class="dashboard-card">
                <h3>📱 Connected Stores</h3>
                <div id="stores-preview">
                    <div style="font-size: 48px; margin-bottom: 15px; text-align: center;">🏪</div>
                    <p id="stores-count" style="text-align: center;">Loading stores...</p>
                </div>
                <div class="quick-actions">
                    <a href="/stores" class="btn btn-primary">
                        Manage Stores
                    </a>
                    <button onclick="loadStoresPreview()" class="btn btn-secondary">
                        Refresh
                    </button>
                </div>
            </div>

            <!-- Instructions Section -->
            <div class="dashboard-card">
                <h3>📋 Instructions</h3>
                <div id="instructions-content">
                    <div style="font-size: 48px; margin-bottom: 15px; text-align: center;">📖</div>
                    <div style="text-align: left; padding: 0 20px;">
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Overview -->
        <div class="dashboard-card">
            <h3>👤 Account Overview</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #666;">Account Status</h4>
                    <span class="status-badge status-active">Active</span>
                </div>
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #666;">Member Since</h4>
                    <p id="member-since" style="margin: 0;">Loading...</p>
                </div>
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #666;">Connected Stores</h4>
                    <p style="margin: 0; font-size: 24px; font-weight: bold; color: #007bff;">0</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-card" style="margin-top: 30px;">
            <h3>⚡ Quick Actions</h3>
            <div class="quick-actions">
                <a href="/profile" class="btn btn-secondary">
                    Edit Profile
                </a>
                <a href="/stores" class="btn btn-primary">
                    Connect Shopify Store
                </a>

                <a href="/docs" class="btn btn-secondary">
                    Documentation
                </a>
            </div>
        </div>
    </div>

    <script>
        // Format date for member since
        function formatMemberSince(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Load user data and populate dashboard
        function loadDashboardData() {
            const userData = JSON.parse(localStorage.getItem('user_data') || '{}');

            if (userData.username) {
                document.getElementById('user-name').textContent = userData.username;
                document.getElementById('user-email').textContent = userData.email;

                if (userData.created_at) {
                    document.getElementById('member-since').textContent = formatMemberSince(userData.created_at);
                }
            }
        }

        // Load stores preview for dashboard
        async function loadStoresPreview() {
            try {
                // Load stores without stats for faster dashboard loading
                const response = await fetch('/stores/', {
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const stores = await response.json();
                    const storesCount = document.getElementById('stores-count');

                    if (stores.length === 0) {
                        storesCount.textContent = 'No stores connected yet';
                    } else {
                        storesCount.textContent = `${stores.length} store${stores.length > 1 ? 's' : ''} connected`;
                    }

                    // Update connected stores count in account overview
                    const accountOverviewElement = document.querySelector('.dashboard-card:nth-child(3)');
                    if (accountOverviewElement) {
                        const storesCountElement = accountOverviewElement.querySelector('div:nth-child(3) p');
                        if (storesCountElement) {
                            storesCountElement.textContent = stores.length;
                        }
                    }
                } else {
                    console.error('Failed to load stores:', response.status, response.statusText);
                    document.getElementById('stores-count').textContent = 'Error loading stores';
                }
            } catch (error) {
                console.error('Error loading stores:', error);
                document.getElementById('stores-count').textContent = 'Error loading stores';
            }
        }





        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            loadStoresPreview();
        });
    </script>

    <script src="/static/js/auth.js"></script>
    <script src="/static/js/widget.js"></script>
</body>
</html>
